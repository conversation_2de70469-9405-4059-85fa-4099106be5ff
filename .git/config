[core]
	repositoryformatversion = 0
	filemode = true
	bare = false
	logallrefupdates = true
	ignorecase = true
	precomposeunicode = true
[user]
	email = <EMAIL>
	name = Sim <PERSON>hen <PERSON>uan
[remote "origin"]
	url = *****************:skribblelab/skribble-learn-api.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "main"]
	remote = origin
	merge = refs/heads/main
	vscode-merge-base = origin/main
	gk-merge-base = origin/main
[branch "qas"]
	remote = origin
	merge = refs/heads/qas
[branch "add-attendance"]
	remote = origin
	merge = refs/heads/add-attendance
[branch "dev"]
	remote = origin
	merge = refs/heads/dev
	vscode-merge-base = origin/main
[branch "timetable-period"]
	remote = origin
	merge = refs/heads/timetable-period
[branch "feature/hostel-report-3.1"]
	remote = origin
	merge = refs/heads/feature/hostel-report-3.1
[branch "bugfix/library-report"]
	remote = origin
	merge = refs/heads/bugfix/library-report
[branch "migration-library-member-others"]
	remote = origin
	merge = refs/heads/migration-library-member-others
[branch "asset-rental"]
	remote = origin
	merge = refs/heads/asset-rental
[branch "hotfix/2025-02-17"]
	vscode-merge-base = origin/main
	remote = origin
	merge = refs/heads/hotfix/2025-02-17
[branch "add-direct-dependant-to-guardian-resource"]
	remote = origin
	merge = refs/heads/add-direct-dependant-to-guardian-resource
[branch "DO-NOT-MERGE-qas-temporary-data"]
	remote = origin
	merge = refs/heads/DO-NOT-MERGE-qas-temporary-data
[branch "feature/qr-code-payment"]
	remote = origin
	merge = refs/heads/feature/qr-code-payment
[branch "enhancement/add-year-to-semester-setting"]
	remote = origin
	merge = refs/heads/enhancement/add-year-to-semester-setting
[pull]
	rebase = false
[branch "path-leave-application-individual-override"]
	remote = origin
	merge = refs/heads/path-leave-application-individual-override
[branch "bugfix/library-book-loan-report-employee-class-relation"]
	remote = origin
	merge = refs/heads/bugfix/library-book-loan-report-employee-class-relation
[branch "fix/attendance-mark-deduction-report"]
	remote = origin
	merge = refs/heads/fix/attendance-mark-deduction-report
[branch "issuelog-25-hostel-savings-see-transaction-balance"]
	remote = origin
	merge = refs/heads/issuelog-25-hostel-savings-see-transaction-balance
[branch "report/teacher-attendance-report"]
	remote = origin
	merge = refs/heads/report/teacher-attendance-report
[branch "issue-124-employee-resigned-with-hostel-bed"]
	remote = origin
	merge = refs/heads/issue-124-employee-resigned-with-hostel-bed
[branch "report/hostel-report-saving-account"]
	remote = origin
	merge = refs/heads/report/hostel-report-saving-account
[branch "lucas-testing"]
	remote = origin
	merge = refs/heads/lucas-testing
[branch "ai-rnd"]
	remote = origin
	merge = refs/heads/ai-rnd
	vscode-merge-base = origin/main
[branch "exam-passing-rate-reports"]
	remote = origin
	merge = refs/heads/exam-passing-rate-reports
[branch "lucas/fix-semester-class-saving-issue"]
	remote = origin
	merge = refs/heads/lucas/fix-semester-class-saving-issue
[branch "bugs/auto-assign-class-subject"]
	remote = origin
	merge = refs/heads/bugs/auto-assign-class-subject
[branch "pinhwa-demo-2025-07-05"]
	remote = origin
	merge = refs/heads/pinhwa-demo-2025-07-05
[branch "SKLEARN-508-randomizer-script-5-for-demo"]
	remote = origin
	merge = refs/heads/SKLEARN-508-randomizer-script-5-for-demo
[branch "SKLEARN-495-exam-mark-entry-enhancements"]
	remote = origin
	merge = refs/heads/SKLEARN-495-exam-mark-entry-enhancements
[branch "fix/update-enrollment"]
	remote = origin
	merge = refs/heads/fix/update-enrollment
[branch "fix/guardian-type-required"]
	remote = origin
	merge = refs/heads/fix/guardian-type-required
[branch "SKLEARN-490-add-translatable-to-title-in-net-average-report"]
	remote = origin
	merge = refs/heads/SKLEARN-490-add-translatable-to-title-in-net-average-report
[branch "comprehensive-result-deadline"]
	remote = origin
	merge = refs/heads/comprehensive-result-deadline
[branch "feature/enrollment-email-template"]
	remote = origin
	merge = refs/heads/feature/enrollment-email-template
[branch "feature/enrollment-student-number"]
	remote = origin
	merge = refs/heads/feature/enrollment-student-number
[branch "SKLEARN-497-reopen-posted-exam-result-by-exam-and-grade"]
	remote = origin
	merge = refs/heads/SKLEARN-497-reopen-posted-exam-result-by-exam-and-grade
[branch "staging/2025-07-21"]
	remote = origin
	merge = refs/heads/staging/2025-07-21
[branch "SKLEARN-193-academy-report-best-grade-by-subject"]
	remote = origin
	merge = refs/heads/SKLEARN-193-academy-report-best-grade-by-subject
[branch "SKLEARN-426-academy-report-all-passed-report"]
	remote = origin
	merge = refs/heads/SKLEARN-426-academy-report-all-passed-report
[branch "SKLEARN-427-academy-report-best-grade-by-class-report"]
	remote = origin
	merge = refs/heads/SKLEARN-427-academy-report-best-grade-by-class-report
[branch "SKLEARN-428-academy-report-best-grade-by-grade"]
	remote = origin
	merge = refs/heads/SKLEARN-428-academy-report-best-grade-by-grade
[branch "feature/hostel-saving-collection-reports"]
	remote = origin
	merge = refs/heads/feature/hostel-saving-collection-reports
[branch "lucas/add-delivery-date-and-available-date-to-product-index-api"]
	remote = origin
	merge = refs/heads/lucas/add-delivery-date-and-available-date-to-product-index-api
[branch "issue-193-student-management-page-employee-only-can-view-their-students"]
	remote = origin
	merge = refs/heads/issue-193-student-management-page-employee-only-can-view-their-students
[branch "SKLEARN-504-green-indicator-for-class-attendance-status-report"]
	remote = origin
	merge = refs/heads/SKLEARN-504-green-indicator-for-class-attendance-status-report
[branch "SKLEARN-559-api-override-changes"]
	remote = origin
	merge = refs/heads/SKLEARN-559-api-override-changes
	vscode-merge-base = origin/SKLEARN-559-api-override-changes
	gk-merge-base = origin/SKLEARN-559-api-override-changes
[branch "fix/employee-update"]
	remote = origin
	merge = refs/heads/fix/employee-update
[branch "fixed-isbn-parse-time-error"]
	remote = origin
	merge = refs/heads/fixed-isbn-parse-time-error
[branch "patch-competition-departments"]
	remote = origin
	merge = refs/heads/patch-competition-departments
[branch "bug/subject-n1"]
	remote = origin
	merge = refs/heads/bug/subject-n1
[branch "support-role-permissions"]
	remote = origin
	merge = refs/heads/support-role-permissions
[branch "SKLEARN-429-academy-report-position-ranking-by-grade-report"]
	remote = origin
	merge = refs/heads/SKLEARN-429-academy-report-position-ranking-by-grade-report
[branch "staging/2025-07-25"]
	remote = origin
	merge = refs/heads/staging/2025-07-25
[branch "SKLEARN-593-only-display-available-teacher-on-substitute-teacher-request-page"]
	remote = origin
	merge = refs/heads/SKLEARN-593-only-display-available-teacher-on-substitute-teacher-request-page
