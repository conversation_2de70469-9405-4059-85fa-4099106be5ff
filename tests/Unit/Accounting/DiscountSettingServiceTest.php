<?php


use App\Helpers\SystemHelper;
use App\Models\Bank;
use App\Models\BankAccount;
use App\Models\BillingDocument;
use App\Models\BillingDocumentLineItem;
use App\Models\DiscountSetting;
use App\Models\Employee;
use App\Models\GlAccount;
use App\Models\LegalEntity;
use App\Models\Payment;
use App\Models\PaymentTerm;
use App\Models\Scholarship;
use App\Models\ScholarshipAward;
use App\Models\Student;
use App\Models\Tax;
use App\Models\UnpaidItem;
use App\Models\User;
use App\Models\WalletTransaction;
use App\Repositories\DiscountSettingRepository;
use App\Services\Billing\DiscountSettingService;
use Carbon\Carbon;
use Database\Seeders\EmployeeSeeder;
use Database\Seeders\PaymentMethodSeeder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Event;
use Mockery\MockInterface;

beforeEach(function () {

    $this->seed([
        PaymentMethodSeeder::class,
        EmployeeSeeder::class,
    ]);

    $this->legalEntity = LegalEntity::factory()->create();
    $this->student = Student::factory()->has(User::factory()->state([]))->create([]);

    $this->system = Employee::where([
        'employee_number' => 'SYSTEM',
    ])->first();

    $this->employee = Employee::factory()->create();

    $this->bank = Bank::factory()->create([
        'name' => 'MAYBANK',
    ]);
    $this->bankAccount = BankAccount::factory()->create([
        'bank_id' => $this->bank->id,
        'bankable_id' => $this->legalEntity->id,
        'bankable_type' => LegalEntity::class,
    ]);
    $this->tax = Tax::factory()->create([
        'percentage' => 0,
        'name' => 'Tax Exempt',
    ]);
    $this->paymentTerm = PaymentTerm::factory()->create([
        'due_date_days' => 10,
    ]);

});

test('test assign discount to multiple students', function () {

    $employee = Employee::factory()->create();

    $scholarship = Scholarship::factory()->create([
        'description' => 'Pin Hwa High School High Achievers Scholarship',
    ]);

    $student2 = Student::factory()->has(User::factory()->state([]))->create([]);
    $student3 = Student::factory()->has(User::factory()->state([]))->create([]);

    $scholarship_award1 = ScholarshipAward::factory()->create([
        'scholarship_id' => $scholarship->id,
        'student_id' => $this->student->id,
    ]);
    $scholarship_award2 = ScholarshipAward::factory()->create([
        'scholarship_id' => $scholarship->id,
        'student_id' => $student2->id,
    ]);
    $scholarship_award3 = ScholarshipAward::factory()->create([
        'scholarship_id' => $scholarship->id,
        'student_id' => $student3->id,
    ]);

    $this->assertDatabaseCount(DiscountSetting::class, 0);

    /** @var DiscountSettingService */
    $service = app()->make(DiscountSettingService::class);

    $service
        ->setBasis(DiscountSetting::BASIS_PERCENT)
        ->setBasisAmount(100)
        ->setMaxAmount(2000)
        ->setEffectiveFromDate('2024-01-01')
        ->setEffectiveToDate('2024-12-31')
        ->setLimitToGlAccountCodes(collect([GlAccount::CODE_OTHERS, 'PRO00000001']))
        ->setDiscountSource($scholarship_award1)
        ->setUserable($this->student)
        ->setCreatedBy($employee)
        ->create();

    $this->assertDatabaseCount(DiscountSetting::class, 1);

    $this->assertDatabaseHas(DiscountSetting::class, [
        'basis' => DiscountSetting::BASIS_PERCENT,
        'basis_amount' => 100,
        'max_amount' => 2000,
        'used_amount' => 0,
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'gl_account_codes->0' => GlAccount::CODE_OTHERS,
        'gl_account_codes->1' => 'PRO00000001',
        'source_type' => $scholarship_award1->getClass(),
        'source_id' => $scholarship_award1->getId(),
        'userable_type' => get_class($this->student),
        'userable_id' => $this->student->id,
        'description' => $scholarship->description,
        'is_active' => false,
        'created_by_employee_id' => $employee->id,
    ]);

    // Test IDiscountSource
    $discount_setting = DiscountSetting::orderBy('id', 'DESC')->first();

    $this->assertEquals($scholarship->description, $discount_setting->source->getDiscountDescription());
    $this->assertEquals($this->student->id, $discount_setting->userable->id);

    $service->setDiscountSource($scholarship_award2)
        ->setUserable($student2)
        ->setCreatedBy(null)
        ->create();

    $this->assertDatabaseCount(DiscountSetting::class, 2);

    $this->assertDatabaseHas(DiscountSetting::class, [
        'basis' => DiscountSetting::BASIS_PERCENT,
        'basis_amount' => 100,
        'max_amount' => 2000,
        'used_amount' => 0,
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'gl_account_codes->0' => GlAccount::CODE_OTHERS,
        'gl_account_codes->1' => 'PRO00000001',
        'source_type' => $scholarship_award2->getClass(),
        'source_id' => $scholarship_award2->getId(),
        'userable_type' => get_class($student2),
        'userable_id' => $student2->id,
        'description' => $scholarship->description,
        'is_active' => false,
        'created_by_employee_id' => null,
    ]);

    // Test IDiscountSource
    $discount_setting = DiscountSetting::orderBy('id', 'DESC')->first();

    $this->assertEquals($scholarship->description, $discount_setting->source->getDiscountDescription());
    $this->assertEquals($student2->id, $discount_setting->userable->id);

    $service->setDiscountSource($scholarship_award3)
        ->setUserable($student3)
        ->create();

    $this->assertDatabaseCount(DiscountSetting::class, 3);

    $this->assertDatabaseHas(DiscountSetting::class, [
        'basis' => DiscountSetting::BASIS_PERCENT,
        'basis_amount' => 100,
        'max_amount' => 2000,
        'used_amount' => 0,
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'gl_account_codes->0' => GlAccount::CODE_OTHERS,
        'gl_account_codes->1' => 'PRO00000001',
        'source_type' => $scholarship_award3->getClass(),
        'source_id' => $scholarship_award3->getId(),
        'userable_type' => get_class($student3),
        'userable_id' => $student3->id,
        'description' => $scholarship->description,
        'is_active' => false,
        'created_by_employee_id' => null,
    ]);

    // Test IDiscountSource
    $discount_setting = DiscountSetting::orderBy('id', 'DESC')->first();

    $this->assertEquals($scholarship->description, $discount_setting->source->getDiscountDescription());
    $this->assertEquals($student3->id, $discount_setting->userable->id);
});

test('test get expired discount settings', function () {

    $setting1 = DiscountSetting::factory()->create([
        'effective_from' => '2024-08-01',
        'effective_to' => '2024-08-31',
        'is_active' => 1,
    ]);

    $setting2 = DiscountSetting::factory()->create([
        'effective_from' => '2024-08-01',
        'effective_to' => '2024-12-31',
        'is_active' => 1,
    ]);

    \Carbon\Carbon::setTestNow('2024-06-01');

    $repository = app()->make(DiscountSettingRepository::class);
    $data = $repository->getAll([
        'past_effective_date' => true,
    ]);

    expect($data->count())->toBe(0);

    \Carbon\Carbon::setTestNow('2024-08-01');

    $repository = app()->make(DiscountSettingRepository::class);
    $data = $repository->getAll([
        'past_effective_date' => true,
    ]);

    expect($data->count())->toBe(0);


    \Carbon\Carbon::setTestNow('2024-08-31');

    $repository = app()->make(DiscountSettingRepository::class);
    $data = $repository->getAll([
        'past_effective_date' => true,
    ]);

    expect($data->count())->toBe(0);

    \Carbon\Carbon::setTestNow('2024-09-01');

    $repository = app()->make(DiscountSettingRepository::class);
    $data = $repository->getAll([
        'past_effective_date' => true,
    ]);

    expect($data->count())->toBe(1)
        ->and($data->first()->id)->toBe($setting1->id);


    \Carbon\Carbon::setTestNow('2025-01-01');

    $repository = app()->make(DiscountSettingRepository::class);
    $data = $repository->getAll([
        'past_effective_date' => true,
    ]);

    expect($data->count())->toBe(2)
        ->and($data->pluck('id')->toArray())->toMatchArray([$setting2->id, $setting1->id]);

});


test('test expire discount settings', function () {

    $setting1 = DiscountSetting::factory()->create([
        'effective_from' => '2024-08-01',
        'effective_to' => '2024-08-31',
        'is_active' => 1,
    ]);

    $setting2 = DiscountSetting::factory()->create([
        'effective_from' => '2024-08-01',
        'effective_to' => '2024-12-31',
        'is_active' => 1,
    ]);

    \Carbon\Carbon::setTestNow('2025-01-01');

    $service = app()->make(DiscountSettingService::class)
        ->expirePastDiscounts();

    $setting1->refresh();
    $setting2->refresh();

    expect($setting1->is_active)->toBeFalse()
        ->and($setting2->is_active)->toBeFalse();

});

test('test get active discounts for userable with 1 apply date', function () {

    // consider applicable
    $setting1 = DiscountSetting::factory()->create([
        'id' => 9000,
        'userable_id' => $this->student->id,
        'userable_type' => get_class($this->student),
        'effective_from' => '2024-08-01',
        'effective_to' => '2024-08-31',
        'is_active' => 1,
    ]);
    // consider applicable
    $setting2 = DiscountSetting::factory()->create([
        'id' => 8999,
        'userable_id' => $this->student->id,
        'userable_type' => get_class($this->student),
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'is_active' => 1,
    ]);
    // consider not applicable (effective date out of range)
    $setting3 = DiscountSetting::factory()->create([
        'userable_id' => $this->student->id,
        'userable_type' => get_class($this->student),
        'effective_from' => '2024-09-01',
        'effective_to' => '2024-10-31',
        'is_active' => 1,
    ]);
    // consider not applicable (is active = 0)
    $setting4 = DiscountSetting::factory()->create([
        'userable_id' => $this->student->id,
        'userable_type' => get_class($this->student),
        'effective_from' => '2024-08-01',
        'effective_to' => '2024-08-31',
        'is_active' => 0,
    ]);

    $repository = app()->make(DiscountSettingRepository::class);

    $data = $repository->getAll([
        'is_active' => true,
        'apply_date' => '2024-08-01',
        'userable_id' => $this->student->id,
        'userable_type' => get_class($this->student),
        'order_by' => [
            'id' => 'asc'
        ]
    ]);

    expect($data->count())->toBe(2)
        ->and($data->pluck('id')->toArray())->toMatchArray([$setting2->id, $setting1->id]);

});

test('test get active discounts for userable with multiple apply dates', function () {

    // consider applicable
    $setting1 = DiscountSetting::factory()->create([
        'id' => 9000,
        'userable_id' => $this->student->id,
        'userable_type' => get_class($this->student),
        'effective_from' => '2024-08-01',
        'effective_to' => '2024-08-31',
        'is_active' => 1,
    ]);
    // consider applicable
    $setting2 = DiscountSetting::factory()->create([
        'id' => 8999,
        'userable_id' => $this->student->id,
        'userable_type' => get_class($this->student),
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'is_active' => 1,
    ]);
    // consider applicable
    $setting3 = DiscountSetting::factory()->create([
        'id' => 8998,
        'userable_id' => $this->student->id,
        'userable_type' => get_class($this->student),
        'effective_from' => '2024-10-01',
        'effective_to' => '2024-10-31',
        'is_active' => 1,
    ]);
    // consider not applicable (effective date out of range)
    $setting4 = DiscountSetting::factory()->create([
        'userable_id' => $this->student->id,
        'userable_type' => get_class($this->student),
        'effective_from' => '2024-11-01',
        'effective_to' => '2024-11-30',
        'is_active' => 1,
    ]);
    // consider not applicable (is active = 0)
    $setting5 = DiscountSetting::factory()->create([
        'userable_id' => $this->student->id,
        'userable_type' => get_class($this->student),
        'effective_from' => '2024-08-01',
        'effective_to' => '2024-08-31',
        'is_active' => 0,
    ]);

    $repository = app()->make(DiscountSettingRepository::class);

    $data = $repository->getAll([
        'is_active' => true,
        'apply_dates' => ['2024-08-01', '2024-10-01'],
        'userable_id' => $this->student->id,
        'userable_type' => get_class($this->student),
        'order_by' => [
            'id' => 'asc'
        ]
    ]);

    expect($data->count())->toBe(3)
        ->and($data->pluck('id')->toArray())->toMatchArray([$setting3->id, $setting2->id, $setting1->id]);

});

test('test get grouped applicable discount for student on specific apply dates', function () {

    // consider applicable (2024-08-01)
    $setting1 = DiscountSetting::factory()->create([
        'id' => 9000,
        'userable_id' => $this->student->id,
        'userable_type' => get_class($this->student),
        'gl_account_codes' => json_encode([GlAccount::CODE_OTHERS, 'PRO00000001']),
        'effective_from' => '2024-08-01',
        'effective_to' => '2024-08-31',
        'is_active' => 1,
    ]);
    // consider applicable  (2024-08-01, 2024-09-01, 2024-10-01)
    $setting2 = DiscountSetting::factory()->create([
        'id' => 8999,
        'userable_id' => $this->student->id,
        'userable_type' => get_class($this->student),
        'gl_account_codes' => json_encode([GlAccount::CODE_OTHERS, 'PRO00000001']),
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-10-31',
        'is_active' => 1,
    ]);
    // consider applicable  (2024-10-01)
    $setting3 = DiscountSetting::factory()->create([
        'id' => 8998,
        'userable_id' => $this->student->id,
        'userable_type' => get_class($this->student),
        'gl_account_codes' => json_encode([GlAccount::CODE_OTHERS]),
        'effective_from' => '2024-10-01',
        'effective_to' => '2024-10-31',
        'is_active' => 1,
    ]);
    // consider applicable (effective date out of range)
    $setting4 = DiscountSetting::factory()->create([
        'userable_id' => $this->student->id,
        'userable_type' => get_class($this->student),
        'effective_from' => '2025-01-01',
        'effective_to' => '2025-11-30',
        'is_active' => 1,
    ]);
    // consider not applicable (hit max limit)
    $setting5 = DiscountSetting::factory()->create([
        'userable_id' => $this->student->id,
        'userable_type' => get_class($this->student),
        'effective_from' => '2024-08-01',
        'effective_to' => '2024-12-31',
        'is_active' => 1,
        'max_amount' => 100,
        'used_amount' => 100,
    ]);

    $service = app()->make(DiscountSettingService::class);

    $data = $service->getAvailableDiscountsForUserable($this->student, ['2024-08-01', '2024-09-01', '2024-10-01', '2024-11-01', '2024-12-01']);

    expect($data)->toHaveKeys(['2024-08-01', '2024-09-01', '2024-10-01', '2024-11-01', '2024-12-01'])
        ->and($data['2024-08-01'])->toHaveKeys([GlAccount::CODE_OTHERS, 'PRO00000001'])
        ->and(collect($data['2024-08-01'][GlAccount::CODE_OTHERS])->pluck('id')->toArray())->toMatchArray([$setting2->id, $setting1->id])
        ->and(collect($data['2024-08-01']['PRO00000001'])->pluck('id')->toArray())->toMatchArray([$setting2->id, $setting1->id])
        ->and($data['2024-09-01'])->toHaveKeys([GlAccount::CODE_OTHERS, 'PRO00000001'])
        ->and(collect($data['2024-09-01'][GlAccount::CODE_OTHERS])->pluck('id')->toArray())->toMatchArray([$setting2->id])
        ->and(collect($data['2024-09-01']['PRO00000001'])->pluck('id')->toArray())->toMatchArray([$setting2->id])
        ->and($data['2024-10-01'])->toHaveKeys([GlAccount::CODE_OTHERS, 'PRO00000001'])
        ->and(collect($data['2024-10-01'][GlAccount::CODE_OTHERS])->pluck('id')->toArray())->toMatchArray([$setting3->id, $setting2->id])
        ->and(collect($data['2024-10-01']['PRO00000001'])->pluck('id')->toArray())->toMatchArray([$setting2->id])
        ->and($data['2024-11-01'])->toBeEmpty()
        ->and($data['2024-12-01'])->toBeEmpty();

    // try to change values in the discount and make sure it's updated by reference to other places
    expect($data['2024-08-01'][GlAccount::CODE_OTHERS][0]->id)
        ->toEqual($data['2024-09-01'][GlAccount::CODE_OTHERS][0]->id);

    expect($data['2024-08-01'][GlAccount::CODE_OTHERS][0])
        ->toBe($data['2024-09-01'][GlAccount::CODE_OTHERS][0]);

    expect($data['2024-08-01'][GlAccount::CODE_OTHERS][0]->used_amount)
        ->toEqual(0)
        ->and($data['2024-09-01'][GlAccount::CODE_OTHERS][0]->used_amount)
        ->toEqual(0);

    $x = $data['2024-08-01'][GlAccount::CODE_OTHERS][0];
    $x->new_attribute = 100;
    $x->used_amount = 101;

    expect($data['2024-08-01'][GlAccount::CODE_OTHERS][0]->used_amount)
        ->toEqual(101)
        ->and($data['2024-09-01'][GlAccount::CODE_OTHERS][0]->used_amount)
        ->toEqual(101)
        ->and($data['2024-08-01'][GlAccount::CODE_OTHERS][0]->new_attribute)
        ->toEqual(100)
        ->and($data['2024-09-01'][GlAccount::CODE_OTHERS][0]->new_attribute)
        ->toEqual(100);

});

test('test no applicable discount for students', function () {

    $service = app()->make(DiscountSettingService::class);

    $data = $service->getAvailableDiscountsForUserable($this->student, ['2024-08-01', '2024-09-01', '2024-10-01', '2024-11-01', '2024-12-01']);

    expect($data)->toHaveKeys(['2024-08-01', '2024-09-01', '2024-10-01', '2024-11-01', '2024-12-01'])
        ->and($data['2024-08-01'])->toBeEmpty()
        ->and($data['2024-09-01'])->toBeEmpty()
        ->and($data['2024-10-01'])->toBeEmpty()
        ->and($data['2024-11-01'])->toBeEmpty()
        ->and($data['2024-12-01'])->toBeEmpty();

});


test('test DiscountSetting calculateDiscountAmount fixed amount basis with max amount', function () {

    $setting1 = DiscountSetting::factory()->create([
        'userable_id' => $this->student->id,
        'userable_type' => get_class($this->student),
        'gl_account_codes' => json_encode([GlAccount::CODE_OTHERS, 'PRO00000001']),
        'effective_from' => '2024-08-01',
        'effective_to' => '2024-10-31',
        'is_active' => 1,
        'max_amount' => 1100,
        'used_amount' => 0,
        'basis' => DiscountSetting::BASIS_FIXED_AMOUNT,
        'basis_amount' => 600
    ]);

    $service = app()->make(DiscountSettingService::class);

    $data = $service->getAvailableDiscountsForUserable($this->student, ['2024-08-01', '2024-09-01', '2024-10-01']);

    expect($data)->toHaveKeys(['2024-08-01', '2024-09-01', '2024-10-01'])
        ->and($data['2024-08-01'])->toHaveKeys([GlAccount::CODE_OTHERS, 'PRO00000001'])
        ->and(collect($data['2024-08-01'][GlAccount::CODE_OTHERS])->pluck('id')->toArray())->toMatchArray([$setting1->id])
        ->and(collect($data['2024-08-01']['PRO00000001'])->pluck('id')->toArray())->toMatchArray([$setting1->id])
        ->and($data['2024-09-01'])->toHaveKeys([GlAccount::CODE_OTHERS, 'PRO00000001'])
        ->and(collect($data['2024-09-01'][GlAccount::CODE_OTHERS])->pluck('id')->toArray())->toMatchArray([$setting1->id])
        ->and(collect($data['2024-09-01']['PRO00000001'])->pluck('id')->toArray())->toMatchArray([$setting1->id])
        ->and($data['2024-10-01'])->toHaveKeys([GlAccount::CODE_OTHERS, 'PRO00000001'])
        ->and(collect($data['2024-10-01'][GlAccount::CODE_OTHERS])->pluck('id')->toArray())->toMatchArray([$setting1->id])
        ->and(collect($data['2024-10-01']['PRO00000001'])->pluck('id')->toArray())->toMatchArray([$setting1->id]);

    $x = $data['2024-08-01'][GlAccount::CODE_OTHERS][0];
    $y = $data['2024-09-01'][GlAccount::CODE_OTHERS][0];

    // line item amount < basis amount, should get full discount based on line item amount
    expect($x->calculateDiscountAmount(400))->toEqual(400);

    expect($data['2024-08-01'][GlAccount::CODE_OTHERS][0]->used_amount)
        ->toEqual(400)
        ->and($data['2024-09-01'][GlAccount::CODE_OTHERS][0]->used_amount)
        ->toEqual(400)
        ->and($data['2024-10-01'][GlAccount::CODE_OTHERS][0]->used_amount)
        ->toEqual(400)
        ->and($data['2024-08-01']['PRO00000001'][0]->used_amount)
        ->toEqual(400)
        ->and($data['2024-09-01']['PRO00000001'][0]->used_amount)
        ->toEqual(400)
        ->and($data['2024-10-01']['PRO00000001'][0]->used_amount)
        ->toEqual(400);

    // line item amount > basis amount, should get up to basis amount discount only
    expect($y->calculateDiscountAmount(800))->toEqual(600);

    expect($data['2024-08-01'][GlAccount::CODE_OTHERS][0]->used_amount)
        ->toEqual(1000)
        ->and($data['2024-09-01'][GlAccount::CODE_OTHERS][0]->used_amount)
        ->toEqual(1000)
        ->and($data['2024-10-01'][GlAccount::CODE_OTHERS][0]->used_amount)
        ->toEqual(1000)
        ->and($data['2024-08-01']['PRO00000001'][0]->used_amount)
        ->toEqual(1000)
        ->and($data['2024-09-01']['PRO00000001'][0]->used_amount)
        ->toEqual(1000)
        ->and($data['2024-10-01']['PRO00000001'][0]->used_amount)
        ->toEqual(1000);


    // line item amount < basis amount, but exceeded max limit, should get up to max limit only.
    expect($y->calculateDiscountAmount(300))->toEqual(100);

    expect($data['2024-08-01'][GlAccount::CODE_OTHERS][0]->used_amount)
        ->toEqual(1100)
        ->and($data['2024-09-01'][GlAccount::CODE_OTHERS][0]->used_amount)
        ->toEqual(1100)
        ->and($data['2024-10-01'][GlAccount::CODE_OTHERS][0]->used_amount)
        ->toEqual(1100)
        ->and($data['2024-08-01']['PRO00000001'][0]->used_amount)
        ->toEqual(1100)
        ->and($data['2024-09-01']['PRO00000001'][0]->used_amount)
        ->toEqual(1100)
        ->and($data['2024-10-01']['PRO00000001'][0]->used_amount)
        ->toEqual(1100);

    // try to apply again when max limit exceeded, should get zero
    expect($x->calculateDiscountAmount(10))->toEqual(0);

    expect($data['2024-08-01'][GlAccount::CODE_OTHERS][0]->used_amount)
        ->toEqual(1100)
        ->and($data['2024-09-01'][GlAccount::CODE_OTHERS][0]->used_amount)
        ->toEqual(1100)
        ->and($data['2024-10-01'][GlAccount::CODE_OTHERS][0]->used_amount)
        ->toEqual(1100)
        ->and($data['2024-08-01']['PRO00000001'][0]->used_amount)
        ->toEqual(1100)
        ->and($data['2024-09-01']['PRO00000001'][0]->used_amount)
        ->toEqual(1100)
        ->and($data['2024-10-01']['PRO00000001'][0]->used_amount)
        ->toEqual(1100);

});


test('test DiscountSetting calculateDiscountAmount percent amount basis with max amount', function () {

    $setting1 = DiscountSetting::factory()->create([
        'userable_id' => $this->student->id,
        'userable_type' => get_class($this->student),
        'gl_account_codes' => json_encode([GlAccount::CODE_OTHERS, 'PRO00000001']),
        'effective_from' => '2024-08-01',
        'effective_to' => '2024-10-31',
        'is_active' => 1,
        'max_amount' => 1000,
        'used_amount' => 0,
        'basis' => DiscountSetting::BASIS_PERCENT,
        'basis_amount' => 80
    ]);

    $service = app()->make(DiscountSettingService::class);

    $data = $service->getAvailableDiscountsForUserable($this->student, ['2024-08-01', '2024-09-01', '2024-10-01']);

    expect($data)->toHaveKeys(['2024-08-01', '2024-09-01', '2024-10-01'])
        ->and($data['2024-08-01'])->toHaveKeys([GlAccount::CODE_OTHERS, 'PRO00000001'])
        ->and(collect($data['2024-08-01'][GlAccount::CODE_OTHERS])->pluck('id')->toArray())->toMatchArray([$setting1->id])
        ->and(collect($data['2024-08-01']['PRO00000001'])->pluck('id')->toArray())->toMatchArray([$setting1->id])
        ->and($data['2024-09-01'])->toHaveKeys([GlAccount::CODE_OTHERS, 'PRO00000001'])
        ->and(collect($data['2024-09-01'][GlAccount::CODE_OTHERS])->pluck('id')->toArray())->toMatchArray([$setting1->id])
        ->and(collect($data['2024-09-01']['PRO00000001'])->pluck('id')->toArray())->toMatchArray([$setting1->id])
        ->and($data['2024-10-01'])->toHaveKeys([GlAccount::CODE_OTHERS, 'PRO00000001'])
        ->and(collect($data['2024-10-01'][GlAccount::CODE_OTHERS])->pluck('id')->toArray())->toMatchArray([$setting1->id])
        ->and(collect($data['2024-10-01']['PRO00000001'])->pluck('id')->toArray())->toMatchArray([$setting1->id]);

    $x = $data['2024-08-01'][GlAccount::CODE_OTHERS][0];
    $y = $data['2024-10-01']['PRO00000001'][0];

    // should get full discount based on line item amount
    expect($x->calculateDiscountAmount(500))->toEqual(400);

    expect($data['2024-08-01'][GlAccount::CODE_OTHERS][0]->used_amount)
        ->toEqual(400)
        ->and($data['2024-09-01'][GlAccount::CODE_OTHERS][0]->used_amount)
        ->toEqual(400)
        ->and($data['2024-10-01'][GlAccount::CODE_OTHERS][0]->used_amount)
        ->toEqual(400)
        ->and($data['2024-08-01']['PRO00000001'][0]->used_amount)
        ->toEqual(400)
        ->and($data['2024-09-01']['PRO00000001'][0]->used_amount)
        ->toEqual(400)
        ->and($data['2024-10-01']['PRO00000001'][0]->used_amount)
        ->toEqual(400);

    // discounted amount exceeded max limit, should get up to max limit only.
    expect($y->calculateDiscountAmount(1000))->toEqual(600);

    expect($data['2024-08-01'][GlAccount::CODE_OTHERS][0]->used_amount)
        ->toEqual(1000)
        ->and($data['2024-09-01'][GlAccount::CODE_OTHERS][0]->used_amount)
        ->toEqual(1000)
        ->and($data['2024-10-01'][GlAccount::CODE_OTHERS][0]->used_amount)
        ->toEqual(1000)
        ->and($data['2024-08-01']['PRO00000001'][0]->used_amount)
        ->toEqual(1000)
        ->and($data['2024-09-01']['PRO00000001'][0]->used_amount)
        ->toEqual(1000)
        ->and($data['2024-10-01']['PRO00000001'][0]->used_amount)
        ->toEqual(1000);

    // try to apply again when max limit exceeded, should get zero
    expect($x->calculateDiscountAmount(100))->toEqual(0);

    expect($data['2024-08-01'][GlAccount::CODE_OTHERS][0]->used_amount)
        ->toEqual(1000)
        ->and($data['2024-09-01'][GlAccount::CODE_OTHERS][0]->used_amount)
        ->toEqual(1000)
        ->and($data['2024-10-01'][GlAccount::CODE_OTHERS][0]->used_amount)
        ->toEqual(1000)
        ->and($data['2024-08-01']['PRO00000001'][0]->used_amount)
        ->toEqual(1000)
        ->and($data['2024-09-01']['PRO00000001'][0]->used_amount)
        ->toEqual(1000)
        ->and($data['2024-10-01']['PRO00000001'][0]->used_amount)
        ->toEqual(1000);

});


test('test getAvailableDiscountsForUserable for a multiple discounts and specific discount', function () {

    $setting1 = DiscountSetting::factory()->create([
        'userable_id' => $this->student->id,
        'userable_type' => get_class($this->student),
        'gl_account_codes' => json_encode([GlAccount::CODE_OTHERS, 'PRO00000001']),
        'effective_from' => '2024-08-01',
        'effective_to' => '2024-10-31',
        'is_active' => 1,
        'max_amount' => 1000,
        'used_amount' => 0,
        'basis' => DiscountSetting::BASIS_PERCENT,
        'basis_amount' => 80
    ]);

    $setting2 = DiscountSetting::factory()->create([
        'userable_id' => $this->student->id,
        'userable_type' => get_class($this->student),
        'gl_account_codes' => json_encode([GlAccount::CODE_OTHERS, 'PRO00000001']),
        'effective_from' => '2024-08-01',
        'effective_to' => '2024-10-31',
        'is_active' => 1,
        'max_amount' => 800,
        'used_amount' => 0,
        'basis' => DiscountSetting::BASIS_PERCENT,
        'basis_amount' => 50
    ]);

    $service = app()->make(DiscountSettingService::class);

    $data = $service->getAvailableDiscountsForUserable($this->student, ['2024-08-01', '2024-09-01', '2024-10-01']);

    expect($data)->toHaveKeys(['2024-08-01', '2024-09-01', '2024-10-01'])
        ->and($data['2024-08-01'])->toHaveKeys([GlAccount::CODE_OTHERS, 'PRO00000001'])
        ->and(collect($data['2024-08-01'][GlAccount::CODE_OTHERS])->pluck('id')->toArray())->toHaveCount(2)->toMatchArray([$setting1->id, $setting2->id])
        ->and(collect($data['2024-08-01']['PRO00000001'])->pluck('id')->toArray())->toHaveCount(2)->toMatchArray([$setting1->id, $setting2->id])
        ->and($data['2024-09-01'])->toHaveKeys([GlAccount::CODE_OTHERS, 'PRO00000001'])
        ->and(collect($data['2024-09-01'][GlAccount::CODE_OTHERS])->pluck('id')->toArray())->toHaveCount(2)->toMatchArray([$setting1->id, $setting2->id])
        ->and(collect($data['2024-09-01']['PRO00000001'])->pluck('id')->toArray())->toHaveCount(2)->toMatchArray([$setting1->id, $setting2->id])
        ->and($data['2024-10-01'])->toHaveKeys([GlAccount::CODE_OTHERS, 'PRO00000001'])
        ->and(collect($data['2024-10-01'][GlAccount::CODE_OTHERS])->pluck('id')->toArray())->toHaveCount(2)->toMatchArray([$setting1->id, $setting2->id])
        ->and(collect($data['2024-10-01']['PRO00000001'])->pluck('id')->toArray())->toHaveCount(2)->toMatchArray([$setting1->id, $setting2->id]);

    $data = $service->getAvailableDiscountsForUserable($this->student, ['2024-08-01', '2024-09-01', '2024-10-01'], [$setting2->id]);

    expect($data)->toHaveKeys(['2024-08-01', '2024-09-01', '2024-10-01'])
        ->and($data['2024-08-01'])->toHaveKeys([GlAccount::CODE_OTHERS, 'PRO00000001'])
        ->and(collect($data['2024-08-01'][GlAccount::CODE_OTHERS])->pluck('id')->toArray())->toHaveCount(1)->toMatchArray([$setting2->id])
        ->and(collect($data['2024-08-01']['PRO00000001'])->pluck('id')->toArray())->toHaveCount(1)->toMatchArray([$setting2->id])
        ->and($data['2024-09-01'])->toHaveKeys([GlAccount::CODE_OTHERS, 'PRO00000001'])
        ->and(collect($data['2024-09-01'][GlAccount::CODE_OTHERS])->pluck('id')->toArray())->toHaveCount(1)->toMatchArray([$setting2->id])
        ->and(collect($data['2024-09-01']['PRO00000001'])->pluck('id')->toArray())->toHaveCount(1)->toMatchArray([$setting2->id])
        ->and($data['2024-10-01'])->toHaveKeys([GlAccount::CODE_OTHERS, 'PRO00000001'])
        ->and(collect($data['2024-10-01'][GlAccount::CODE_OTHERS])->pluck('id')->toArray())->toHaveCount(1)->toMatchArray([$setting2->id])
        ->and(collect($data['2024-10-01']['PRO00000001'])->pluck('id')->toArray())->toHaveCount(1)->toMatchArray([$setting2->id]);

});


test('test getAvailableDiscountsByGroup', function () {
    $student = Student::factory()->create();

    $discount1 = DiscountSetting::factory()->create([
        'userable_id' => $student->id,
        'userable_type' => get_class($student),
        'gl_account_codes' => json_encode([GlAccount::CODE_OTHERS]),
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'is_active' => 1,
    ]);

    $discount2 = DiscountSetting::factory()->create([
        'userable_id' => $student->id,
        'userable_type' => get_class($student),
        'gl_account_codes' => json_encode([GlAccount::CODE_SCHOOL_FEES]),
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'is_active' => 1,
    ]);

    $userable_discounts = collect([$discount1, $discount2]);

    $apply_dates = ['2024-01-01', '2024-06-01'];

    $service = app()->make(DiscountSettingService::class);

    $grouped_discounts = $service->getAvailableDiscountsByGroup($userable_discounts, $apply_dates);

    expect($grouped_discounts)->toHaveKeys(['2024-01-01', '2024-06-01'])
        ->and($grouped_discounts['2024-01-01'])->toHaveKeys([GlAccount::CODE_OTHERS, GlAccount::CODE_SCHOOL_FEES])
        ->and($grouped_discounts['2024-01-01'][GlAccount::CODE_OTHERS])->toHaveCount(1)
        ->and($grouped_discounts['2024-01-01'][GlAccount::CODE_SCHOOL_FEES])->toHaveCount(1)
        ->and($grouped_discounts['2024-01-01'][GlAccount::CODE_OTHERS][0]->id)->toBe($discount1->id)
        ->and($grouped_discounts['2024-01-01'][GlAccount::CODE_SCHOOL_FEES][0]->id)->toBe($discount2->id)
        ->and($grouped_discounts['2024-06-01'])->toHaveKeys([GlAccount::CODE_OTHERS, GlAccount::CODE_SCHOOL_FEES])
        ->and($grouped_discounts['2024-06-01'][GlAccount::CODE_OTHERS])->toHaveCount(1)
        ->and($grouped_discounts['2024-06-01'][GlAccount::CODE_SCHOOL_FEES])->toHaveCount(1)
        ->and($grouped_discounts['2024-06-01'][GlAccount::CODE_OTHERS][0]->id)->toBe($discount1->id)
        ->and($grouped_discounts['2024-06-01'][GlAccount::CODE_SCHOOL_FEES][0]->id)->toBe($discount2->id);
});

test('test getPaidInvoicesEligibleForBackApply', function () {

    // paid invoice, eligible for backapply discount
    $invoice1 = BillingDocument::factory()->create([
        'document_date' => Carbon::now()->toDateString(),
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'amount_before_tax' => 100,
        'amount_after_tax' => 100,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => 'Lucas',
        'bill_to_address' => '123, Jalan ABC, 47000 Selangor.',
        'paid_at' => '2024-07-25 04:30:00'
    ]);

    // paid invoice, eligible for backapply discount
    $invoice2 = BillingDocument::factory()->create([
        'document_date' => Carbon::now()->toDateString(),
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'amount_before_tax' => 100,
        'amount_after_tax' => 100,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => 'Lucas',
        'bill_to_address' => '123, Jalan ABC, 47000 Selangor.',
        'paid_at' => '2024-07-25 04:30:00'
    ]);

    // unpaid invoice
    $invoice3 = BillingDocument::factory()->create([
        'document_date' => Carbon::now()->toDateString(),
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
        'amount_before_tax' => 100,
        'amount_after_tax' => 100,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => 'Lucas',
        'bill_to_address' => '123, Jalan ABC, 47000 Selangor.',
        'paid_at' => '2024-07-25 04:30:00'
    ]);

    $discount = DiscountSetting::factory()->create([
        'userable_id' => $this->student->id,
        'userable_type' => get_class($this->student),
        'gl_account_codes' => json_encode(['SCH00000001', 'HOS00000001']),
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'is_active' => 1,
        'max_amount' => null,
        'used_amount' => 0,
        'basis' => DiscountSetting::BASIS_PERCENT,
        'basis_amount' => 50
    ]);

    $unpaid_item1 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 400,
        'period' => '2024-01-01',
        'gl_account_code' => 'SCH00000001',
    ]);
    $unpaid_item2 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 300,
        'period' => '2024-02-01',
        'gl_account_code' => 'SCH00000001',
    ]);
    $unpaid_item3 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 600,
        'period' => '2024-01-01',
        'gl_account_code' => 'XXX00000001',
    ]);
    $unpaid_item4 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 600,
        'period' => '2024-02-01',
        'gl_account_code' => 'XXX00000001',
    ]);
    $unpaid_item5 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 600,
        'period' => '2023-12-01',
        'gl_account_code' => 'SCH00000001',
    ]);
    $unpaid_item6 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 300,
        'period' => '2024-03-01',
        'gl_account_code' => 'SCH00000001',
    ]);

    $line_item1 = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice1,
        'amount_before_tax' => 400,
        'gl_account_code' => 'SCH00000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item1->id,
        'billable_item_type' => get_class($unpaid_item1),
        'description' => 'School Fees Jan 2024',
    ]);
    $line_item2 = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice1,
        'amount_before_tax' => 600,
        'gl_account_code' => 'XXX00000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item3->id,
        'billable_item_type' => get_class($unpaid_item3),
        'description' => 'Other Fees Jan 2024',
    ]);
    $line_item3 = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice1,
        'amount_before_tax' => 400,
        'gl_account_code' => 'SCH00000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item5->id,
        'billable_item_type' => get_class($unpaid_item5),
        'description' => 'School Fees Dec 2023',
    ]);

    $line_item4 = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice2,
        'amount_before_tax' => 300,
        'gl_account_code' => 'SCH00000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item2->id,
        'billable_item_type' => get_class($unpaid_item2),
        'description' => 'School Fees Feb 2024',
    ]);
    $line_item5 = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice2,
        'amount_before_tax' => 600,
        'gl_account_code' => 'YYY00000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item4->id,
        'billable_item_type' => get_class($unpaid_item4),
        'description' => 'Other Fees Feb 2024',
    ]);

    $line_item6 = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice3,
        'amount_before_tax' => 400,
        'gl_account_code' => 'SCH00000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item6->id,
        'billable_item_type' => get_class($unpaid_item6),
        'description' => 'School Fees March 2024',
    ]);

    $wallet_transaction = WalletTransaction::factory()->create([
        'userable_id' => $this->student->id,
        'userable_type' => get_class($this->student),
        'total_amount' => 100,
        'amount_before_tax' => 100,
        'amount_after_tax' => 100,
        'balance_before' => 100,
        'balance_after' => 0,
    ]);
    $unrelated_line_item = BillingDocumentLineItem::factory()->create([ // this line item will be excluded from discount related calculations because it is of type WalletTransaction
        'billing_document_id' => $invoice1,
        'amount_before_tax' => 400,
        'gl_account_code' => 'SCH00000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $wallet_transaction->id,
        'billable_item_type' => get_class($wallet_transaction),
        'description' => 'Wallet Transaction Dec 2023',
    ]);

    $data = app()->make(DiscountSettingService::class)
        ->setDiscount($discount)
        ->getPaidInvoicesEligibleForBackApply();

    expect($data)->toHaveCount(2)
        ->and($data[0])->toBeInstanceOf(BillingDocument::class)
        ->and($data[0]->id)->toBe($invoice1->id)
        ->and($data[1])->toBeInstanceOf(BillingDocument::class)
        ->and($data[1]->id)->toBe($invoice2->id);

    // try change effective period of discount (no match)
    $discount->effective_from = '2025-01-01';
    $discount->effective_to = '2025-12-01';
    $discount->save();

    $data = app()->make(DiscountSettingService::class)
        ->setDiscount($discount)
        ->getPaidInvoicesEligibleForBackApply();

    expect($data)->toHaveCount(0);

    // try change effective period of discount (partial match inv1 only)
    $discount->effective_from = '2024-01-01';
    $discount->effective_to = '2024-01-01';
    $discount->save();

    $data = app()->make(DiscountSettingService::class)
        ->setDiscount($discount)
        ->getPaidInvoicesEligibleForBackApply();

    expect($data)->toHaveCount(1)
        ->and($data[0])->toBeInstanceOf(BillingDocument::class)
        ->and($data[0]->id)->toBe($invoice1->id);

    // reset effective period
    $discount->effective_from = '2024-01-01';
    $discount->effective_to = '2024-12-31';
    $discount->save();

    // try change gl account of discount (no match)
    $discount->gl_account_codes = json_encode(['ZZZ00000001']);
    $discount->save();

    $data = app()->make(DiscountSettingService::class)
        ->setDiscount($discount)
        ->getPaidInvoicesEligibleForBackApply();

    expect($data)->toHaveCount(0);

    // try change gl account of discount (match inv2 only)
    $discount->gl_account_codes = json_encode(['YYY00000001']);
    $discount->save();

    $data = app()->make(DiscountSettingService::class)
        ->setDiscount($discount)
        ->getPaidInvoicesEligibleForBackApply();

    expect($data)->toHaveCount(1)
        ->and($data[0])->toBeInstanceOf(BillingDocument::class)
        ->and($data[0]->id)->toBe($invoice2->id);
});


test('test backApplyDiscountAsAdvancePayment', function () {

    // paid invoice, eligible for backapply discount
    $invoice1 = BillingDocument::factory()->create([
        'legal_entity_id' => $this->legalEntity->id,
        'payment_term_id' => $this->paymentTerm->id,
        'document_date' => Carbon::now()->toDateString(),
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'amount_before_tax' => 100,
        'amount_after_tax' => 100,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => 'Lucas',
        'bill_to_address' => '123, Jalan ABC, 47000 Selangor.',
        'paid_at' => '2024-07-25 04:30:00',
        'tax_code' => $this->tax->code,
    ]);

    // paid invoice, eligible for backapply discount
    $invoice2 = BillingDocument::factory()->create([
        'legal_entity_id' => $this->legalEntity->id,
        'payment_term_id' => $this->paymentTerm->id,
        'document_date' => Carbon::now()->toDateString(),
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'amount_before_tax' => 100,
        'amount_after_tax' => 100,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => 'Lucas',
        'bill_to_address' => '123, Jalan ABC, 47000 Selangor.',
        'paid_at' => '2024-07-25 04:30:00',
        'tax_code' => $this->tax->code,
    ]);

    $discount = DiscountSetting::factory()->create([
        'userable_id' => $this->student->id,
        'userable_type' => get_class($this->student),
        'gl_account_codes' => json_encode(['SCH00000001', 'HOS00000001']),
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'is_active' => 1,
        'max_amount' => null,
        'used_amount' => 0,
        'basis' => DiscountSetting::BASIS_PERCENT,
        'basis_amount' => 50
    ]);

    $unpaid_item1 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 400,
        'period' => '2024-01-01',
        'gl_account_code' => 'SCH00000001',
    ]);
    $unpaid_item2 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 300,
        'period' => '2024-02-01',
        'gl_account_code' => 'SCH00000001',
    ]);
    $unpaid_item3 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 600,
        'period' => '2024-01-01',
        'gl_account_code' => 'XXX00000001',
    ]);
    $unpaid_item4 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 600,
        'period' => '2024-02-01',
        'gl_account_code' => 'XXX00000001',
    ]);
    $unpaid_item5 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 600,
        'period' => '2023-12-01',
        'gl_account_code' => 'SCH00000001',
    ]);
    $unpaid_item6 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => null,
        'amount_before_tax' => 300,
        'period' => '2024-03-01',
        'gl_account_code' => 'SCH00000001',
    ]);

    $line_item1 = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice1,
        'amount_before_tax' => 400,
        'gl_account_code' => 'SCH00000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item1->id,
        'billable_item_type' => get_class($unpaid_item1),
        'description' => 'School Fees Jan 2024',
    ]);
    $line_item2 = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice1,
        'amount_before_tax' => 600,
        'gl_account_code' => 'XXX00000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item3->id,
        'billable_item_type' => get_class($unpaid_item3),
        'description' => 'Other Fees Jan 2024',
    ]);
    $line_item3 = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice1,
        'amount_before_tax' => 400,
        'gl_account_code' => 'SCH00000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item5->id,
        'billable_item_type' => get_class($unpaid_item5),
        'description' => 'School Fees Dec 2023',
    ]);

    $line_item4 = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice2,
        'amount_before_tax' => 300,
        'gl_account_code' => 'SCH00000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item2->id,
        'billable_item_type' => get_class($unpaid_item2),
        'description' => 'School Fees Feb 2024',
    ]);
    $line_item5 = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice2,
        'amount_before_tax' => 600,
        'gl_account_code' => 'HOS00000001',
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item4->id,
        'billable_item_type' => get_class($unpaid_item4),
        'description' => 'Hostel Fees Feb 2024',
    ]);

    $this->assertDatabaseCount(BillingDocument::class, 2);
    Carbon::setTestNow('2024-08-20 13:02:00');

    Event::fake();

    app()->make(DiscountSettingService::class)
        ->setDiscount($discount)
        ->backApplyDiscountAsAdvancePayment();

    // should get these advances
    // invoice 1 - line item 1 - RM 200
    // invoice 2 - line item 4 - RM 150
    // invoice 2 - line item 5 - RM 300

    $this->assertDatabaseCount(BillingDocument::class, 4);

    $this->assertDatabaseHas(BillingDocument::class, [
        'type' => BillingDocument::TYPE_ADVANCE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'document_date' => '2024-08-20',
        'reference_no' => 'AINV202400001',
        'posting_date' => null,
        'legal_entity_id' => $invoice1->legal_entity_id,
        'tax_code' => $invoice1->tax_code,
        'bill_to_type' => $invoice1->bill_to_type,
        'bill_to_id' => $invoice1->bill_to_id,
        'payment_term_id' => $invoice1->payment_term_id,
        'currency_code' => $invoice1->currency_code,
        'amount_before_tax' => 200,
        'amount_before_tax_after_less_advance' => 200,
        'tax_amount' => 0,
        'amount_after_tax' => 200,
    ]);


    $advance_invoice_1 = BillingDocument::where('type', BillingDocument::TYPE_ADVANCE_INVOICE)->where('amount_before_tax', 200)->first();

    expect($advance_invoice_1->lineItems)->toHaveCount(1);

    $this->assertDatabaseHas(Payment::class, [
        'amount_received' => 200,
        'billing_document_id' => $advance_invoice_1->id,
        'payment_reference_no' => $advance_invoice_1->reference_no . '-PAID',
        'payment_method_id' => \App\Helpers\SystemHelper::getSystemPaymentMethod()->id,
        'payment_source_type' => null,
        'payment_source_id' => null,
        'created_by_employee_id' => SystemHelper::getSystemEmployee()->id,
    ]);

    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $advance_invoice_1->id,
        'description' => 'Discounted amount as advance payment from ' . $invoice1->reference_no . ' - ' . $line_item1->description,
        'amount_before_tax' => 200,
        'gl_account_code' => 'SCH00000001',
    ]);

    $this->assertDatabaseHas(BillingDocument::class, [
        'type' => BillingDocument::TYPE_ADVANCE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'document_date' => '2024-08-20',
        'reference_no' => 'AINV202400002',
        'posting_date' => null,
        'legal_entity_id' => $invoice2->legal_entity_id,
        'tax_code' => $invoice2->tax_code,
        'bill_to_type' => $invoice2->bill_to_type,
        'bill_to_id' => $invoice2->bill_to_id,
        'payment_term_id' => $invoice2->payment_term_id,
        'currency_code' => $invoice2->currency_code,
        'amount_before_tax' => 450,
        'amount_before_tax_after_less_advance' => 450,
        'tax_amount' => 0,
        'amount_after_tax' => 450,
    ]);


    $advance_invoice_2 = BillingDocument::where('type', BillingDocument::TYPE_ADVANCE_INVOICE)->where('amount_before_tax', 450)->first();

    expect($advance_invoice_2->lineItems)->toHaveCount(2);

    $this->assertDatabaseHas(Payment::class, [
        'amount_received' => 450,
        'billing_document_id' => $advance_invoice_2->id,
        'payment_reference_no' => $advance_invoice_2->reference_no . '-PAID',
        'payment_method_id' => \App\Helpers\SystemHelper::getSystemPaymentMethod()->id,
        'payment_source_type' => null,
        'payment_source_id' => null,
        'created_by_employee_id' => SystemHelper::getSystemEmployee()->id,
    ]);
    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $advance_invoice_2->id,
        'description' => 'Discounted amount as advance payment from ' . $invoice2->reference_no . ' - ' . $line_item4->description,
        'amount_before_tax' => 150,
        'gl_account_code' => 'SCH00000001',
    ]);
    $this->assertDatabaseHas(BillingDocumentLineItem::class, [
        'billing_document_id' => $advance_invoice_2->id,
        'description' => 'Discounted amount as advance payment from ' . $invoice2->reference_no . ' - ' . $line_item5->description,
        'amount_before_tax' => 300,
        'gl_account_code' => 'HOS00000001',
    ]);

    Event::assertDispatched(\App\Events\InvoicePaidEvent::class);

});


test('getAllDiscountSettings', function () {

    $filters = ['order_by' => 'id'];

    $this->mock(DiscountSettingRepository::class, function (MockInterface $mock) use ($filters) {
        $mock->shouldReceive('getAll')->once()->with($filters);
    });

    resolve(DiscountSettingService::class)->getAllDiscountSettings($filters);
});

test('getAllPaginatedDiscountSettings', function (int $expected_count, string $filter_by, mixed $filter_value, array $expected_model) {
    $student = Student::factory()->create();

    $filter_options = [
        'student' => [
            'userable_type' => get_class($student),
            'userable_id' => $student->id,
        ],
    ];

    $actual_filters = isset($filter_options[$filter_by]) ? $filter_options[$filter_by] : [$filter_by => $filter_value];

    $this->mock(DiscountSettingRepository::class, function (MockInterface $mock) use ($actual_filters) {
        $mock->shouldReceive('getAllPaginated')->once()->with($actual_filters)->andReturn(new LengthAwarePaginator([], 1, 1));
    });

    resolve(DiscountSettingService::class)->getAllPaginatedDiscountSettings($actual_filters);

})->with([
    'filter by student' => [1, 'student', null, ['fifth']],
]);
