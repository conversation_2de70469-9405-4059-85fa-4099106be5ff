<?php

namespace App\Repositories;

use App\Enums\ClassType;
use App\Models\BillingDocument;
use App\Models\BillingDocumentLineItem;
use App\Models\PaymentRequest;
use App\Models\Student;
use App\Models\UnpaidItem;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Query\JoinClause;

class BillingDocumentRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return BillingDocument::class;
    }

    public function getQuery($filters = []): Builder
    {
        $query = parent::getQuery($filters);

        return $query
            ->when(isset($filters['bill_to_type']) && isset($filters['bill_to_id']), function (Builder $query) use ($filters) {
                $query->where('bill_to_type', $filters['bill_to_type'])
                    ->where('bill_to_id', $filters['bill_to_id']);
            })
            ->when(isset($filters['id']), function (Builder $query) use ($filters) {
                $query->where('id', $filters['id']);
            })
            ->when(isset($filters['status']), function (Builder $query) use ($filters) {
                $query->where('status', $filters['status']);
            })
            ->when(isset($filters['payment_status']), function (Builder $query) use ($filters) {
                $query->where('payment_status', $filters['payment_status']);
            })
            ->when(isset($filters['type']), function (Builder $query) use ($filters) {
                $query->where('type', $filters['type']);
            })
            ->when(isset($filters['sub_type']), function (Builder $query) use ($filters) {
                $query->where('sub_type', $filters['sub_type']);
            })
            ->when(isset($filters['paid_at_from']) && isset($filters['paid_at_to']), function (Builder $query) use ($filters) {
                $query->where('paid_at', '>=', $filters['paid_at_from'])
                    ->where('paid_at', '<=', $filters['paid_at_to']);
            })
            ->when(isset($filters['document_date_from']) && isset($filters['document_date_to']), function (Builder $query) use ($filters) {
                $query->where('document_date', '>=', $filters['document_date_from'])
                    ->where('document_date', '<=', $filters['document_date_to']);
            })
            ->when(isset($filters['reference_no']), function (Builder $query) use ($filters) {
                $query->where('reference_no', $filters['reference_no']);
            })
            ->when(isset($filters['document_date_from']) && isset($filters['document_date_to']), function (Builder $query) use ($filters) {
                $query->where('document_date', '>=', $filters['document_date_from'])
                    ->where('document_date', '<=', $filters['document_date_to']);
            })
            ->when(isset($filters['product_ids']) && count($filters['product_ids']) > 0, function (Builder $query) use ($filters) {
                $query->whereHas('lineItems.product', function ($q) use ($filters) {
                    $q->whereIn('id', $filters['product_ids']);
                });
            })
            ->when(isset($filters['payment_method_id']), function (Builder $query) use ($filters) {
                $query->whereRelation('payments', 'payment_method_id', $filters['payment_method_id']);
            })
            ->when(isset($filters['payment_reference_no']), function (Builder $query) use ($filters) {
                $query->whereHas('payments', function ($q) use ($filters) {
                    $q->where('payment_reference_no', $filters['payment_reference_no']);
                });
            });

    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }

    public function getPaidInvoicesEligibleForBackApply($filters = [])
    {
        // get line items that belonging to student, and within billable item period, with correct gl account code, and invoice is paid.
        $line_items = BillingDocumentLineItem::query()
            ->whereHas('billingDocument', function ($query) use (&$filters) {
                $query->where('bill_to_id', $filters['bill_to_id'])
                    ->where('bill_to_type', $filters['bill_to_type'])
                    ->where('payment_status', BillingDocument::PAYMENT_STATUS_PAID)
                    ->where('type', BillingDocument::TYPE_INVOICE);
            })
            ->whereHasMorph(
                'billableItem',
                [UnpaidItem::class], // Only check UnpaidItem
                function ($query) use (&$filters) {
                    $query->where('period', '>=', $filters['period_from'])
                        ->where('period', '<=', $filters['period_to']);
                }
            )
            ->whereIn('gl_account_code', $filters['gl_account_codes'])
            ->get();

        // then check whether these line items have corresponding discount line items
        $line_items_with_discount = BillingDocumentLineItem::select(['discount_original_line_item_id'])
            ->where('is_discount', true)
            ->whereIn('discount_original_line_item_id', $line_items->pluck('id'))
            ->get()
            ->pluck('discount_original_line_item_id');

        // only return invoices with line items that have no corresponding discount line items (not discounted before)
        $eligible_line_items = $line_items->filter(function ($line_item) use (&$line_items_with_discount) {
            if ($line_items_with_discount->contains($line_item->id)) {
                return false;
            }
            return true;
        });

        $eligible_billing_documents = $eligible_line_items->pluck('billingDocument')->unique()->values();

        return $eligible_billing_documents;

    }

    public function getHostelSavingsAccountTransactions(array $filters = [], $pagination = false)
    {

        $query = $this->getQuery($filters)
            ->with(['payments', 'lineItems'])
            ->whereIn('status', [BillingDocument::STATUS_CONFIRMED, BillingDocument::STATUS_POSTED])
            ->where('sub_type', BillingDocument::SUB_TYPE_HOSTEL_SAVINGS_ACCOUNT)
            ->orderBy('paid_at', 'desc')
            ->orderBy('id', 'desc');

        if ($pagination) {
            return $query->paginate($this->per_page);
        } else {
            return $query->get();
        }
    }

    public function getPaidInvoiceReportData(array $filters = []): Collection
    {
        return $this->getInvoiceReportData(
            $filters,
            BillingDocument::SUB_TYPE_FEES,
            true // include class and grade info
        );
    }

    public function getPaidEnrollmentInvoiceReportData(array $filters = []): Collection
    {
        return $this->getInvoiceReportData(
            $filters,
            BillingDocument::SUB_TYPE_ENROLLMENT_FEES,
            false // no need class
        );
    }

    private function getInvoiceReportData(array $filters = [], string $sub_type = '', bool $include_class_and_grade = true): Collection
    {
        $parent_filters = [
            'includes' => [
                'billTo' => function ($query) {
                    $query->select(['id', 'name']);
                },
                'lineItems' => function ($query) {
                    $query->select(['id', 'billing_document_id', 'description', 'is_discount', 'offset_billing_document_id', 'amount_before_tax', 'product_id', 'discount_original_line_item_id', 'billable_item_type', 'billable_item_id']);
                    $query->with([
                        'product' => function ($query) {
                            $query->select(['id', 'name']);
                        },
                        'billableItem' => function ($query) {
                            $query->select(['id', 'period', 'description', 'product_id']);
                        },
                    ]);
                    $query->orderBy('id', 'asc');
                },
                'payments' => function ($query) {
                    $query->select(['id', 'billing_document_id', 'payment_method_id', 'payment_reference_no', 'payment_source_type', 'payment_source_id']);
                    $query->with([
                        'paymentMethod' => function ($query) {
                            $query->select(['id', 'code']);
                        },
                        'paymentSource' => function (MorphTo $morphTo) {
                            $morphTo->morphWith([
                                PaymentRequest::class => ['bank'],
                            ]);
                        },
                    ]);
                },
            ]
        ];

        $query = $this->getQuery($parent_filters);

        if ($include_class_and_grade) {
            $query->selectRaw('id, document_date, reference_no, bill_to_type, bill_to_id, bill_to_reference_number, class_name->>\'' . app()->getLocale() . '\' AS class_name, grade_name->>\'' . app()->getLocale() . '\' AS grade_name, paid_at, currency_code, amount_after_tax');

            $query->leftJoin('current_students_class_and_grade_view', function (JoinClause $join) {
                $join->on('current_students_class_and_grade_view.student_id', '=', 'billing_documents.bill_to_id')
                    ->where('billing_documents.bill_to_type', '=', Student::class)
                    ->where('class_type', ClassType::PRIMARY->value);
            });
        } else {
            $query->selectRaw('id, document_date, reference_no, bill_to_type, bill_to_id, bill_to_reference_number, paid_at, currency_code, amount_after_tax');
        }

        $query->where('status', BillingDocument::STATUS_CONFIRMED)
            ->where('payment_status', BillingDocument::PAYMENT_STATUS_PAID)
            ->where('type', BillingDocument::TYPE_INVOICE)
            ->where('sub_type', $sub_type)
            ->when(isset($filters['payment_date_from']), function (Builder $query) use ($filters) {
                $query->where('paid_at', '>=', Carbon::parse($filters['payment_date_from'], config('school.timezone'))->startOfDay()->tz('UTC'));
            })
            ->when(isset($filters['payment_date_to']), function (Builder $query) use ($filters) {
                $query->where('paid_at', '<=', Carbon::parse($filters['payment_date_to'], config('school.timezone'))->endOfDay()->tz('UTC'));
            })
            ->when(isset($filters['product_ids']) && count($filters['product_ids']) > 0, function (Builder $query) use ($filters) {
                $query->whereHas('lineItems.product', function ($q) use ($filters) {
                    $q->whereIn('id', $filters['product_ids']);
                });
            })
            ->orderBy('paid_at', 'desc');

        return $query->get();
    }
}
