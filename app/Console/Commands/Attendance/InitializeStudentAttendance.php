<?php

namespace App\Console\Commands\Attendance;

use App\Enums\AttendanceStatus;
use App\Enums\Day;
use App\Enums\LeaveApplicationStatus;
use App\Enums\PeriodAttendanceStatus;
use App\Helpers\LogHelper;
use App\Models\Attendance;
use App\Models\Student;
use App\Repositories\AttendanceRepository;
use App\Repositories\CalendarTargetRepository;
use App\Repositories\LeaveApplicationPeriodRepository;
use App\Repositories\PeriodAttendanceRepository;
use App\Services\Timetable\StudentTimetableService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Psr\Log\LogLevel;

class InitializeStudentAttendance extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'init:student-attendance {--date=default}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Bulk initialize student attendance at the beginning of each day.';

    public function __construct(
        protected PeriodAttendanceRepository $periodAttendanceRepository,
        protected StudentTimetableService $studentTimetableService,
        protected LeaveApplicationPeriodRepository $leaveApplicationPeriodRepository,
        protected AttendanceRepository $attendanceRepository,
        protected CalendarTargetRepository $calendarTargetRepository,
    ) {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {

            if ($this->option('date') === 'default') {
                $datetime_school_timezone = Carbon::now(config('school.timezone'));
            } else {
                $datetime_school_timezone = Carbon::parse($this->option('date'), config('school.timezone'));
            }

            $date = $datetime_school_timezone->toDateString();
            $day = Day::carbonWeekdayToDay($datetime_school_timezone->dayOfWeek);

            $this->info('Initializing attendance records for period ' . $date);
            LogHelper::write('Initializing attendance records for period ' . $date, null, LogLevel::INFO, LogHelper::LOG_NAME_ATTENDANCE);

            $existing_student_attendances = Attendance::where('date', $date)
                ->where('attendance_recordable_type', Student::class)
                ->pluck('attendance_recordable_id');

            if (count($existing_student_attendances) > 0) {
                $this->info('Students with existing attendance records: ' . $existing_student_attendances->join(', '));
                LogHelper::write('Students with existing attendance records: ' . $existing_student_attendances->join(', '), null, LogLevel::INFO, LogHelper::LOG_NAME_ATTENDANCE);
            }

            Student::getActiveStudents([], [], ['id'])
                ->chunkById(200, function ($students) use ($date, $day, $existing_student_attendances) {
                    DB::transaction(function () use ($existing_student_attendances, $date, $day, $students) {
                        $attendance_to_be_created = [];
                        $period_attendance_to_be_created = [];
                        $student_ids_that_required_attendance = [];

                        $students_school_is_attendance_required = $this->calendarTargetRepository->getStudentsSchoolDayOrNonSchoolDayByDate($date, $students->pluck('id')->toArray());
                        foreach ($students as $student) {
                            // only post for students with calendar + is_attendance_required = true
                            if (isset($students_school_is_attendance_required[$student->id]) && $students_school_is_attendance_required[$student->id] == true) {
                                $student_ids_that_required_attendance[] = $student->id;
                            }
                        }

                        $student_timetable_list_by_date_and_student = collect($this->studentTimetableService
                            ->setDayFilter($day)
                            ->setStudentIds($student_ids_that_required_attendance)
                            ->getTimetableWithTimeslotOverrideByDate([$date])
                        );

                        $existing_period_attendances = $this->periodAttendanceRepository->getAll([
                            'date' => $date,
                            'student_id' => $student_ids_that_required_attendance,
                        ])
                            ->groupBy(['student_id', 'period'])
                            ->all();

                        $existing_leave_application_periods = $this->leaveApplicationPeriodRepository
                            ->getQuery([
                                'leave_application_date' => $date,
                            ])
                            ->whereHas('leaveApplication', function ($query) use ($student_ids_that_required_attendance) {
                                $query->where('leave_applicable_type', Student::class)
                                    ->whereIn('leave_applicable_id', $student_ids_that_required_attendance)
                                    ->where('status', LeaveApplicationStatus::APPROVED->value);
                            })
                            ->get()
                            ->groupBy(['leaveApplication.leave_applicable_id'])
                            ->map(function ($leave_application_periods) {
                                return $leave_application_periods->keyBy('period');
                            });


                        foreach ($student_ids_that_required_attendance as $student_id) {

                            // attendance
                            if (!in_array($student_id, $existing_student_attendances->toArray())) {
                                $attendance_to_be_created[] = [
                                    'attendance_recordable_type' => Student::class,
                                    'attendance_recordable_id' => $student_id,
                                    'date' => $date,
                                    'check_in_datetime' => null,
                                    'check_in_status' => null,
                                    'check_in_remarks' => null,
                                    'check_out_datetime' => null,
                                    'check_out_status' => null,
                                    'check_out_remarks' => null,
                                    'status' => AttendanceStatus::ABSENT,
                                    'created_at' => now(),
                                    'updated_at' => now(),
                                ];
                            }

                            // period attendance
                            $student_timetable_timeslots = $student_timetable_list_by_date_and_student[$date][$student_id] ?? null;
                            $student_leave_application_periods = $existing_leave_application_periods[$student_id] ?? null;
                            // not creating period attendance for student without timetable
                            if ($student_timetable_timeslots) {
                                foreach ($student_timetable_timeslots as $data) {
                                    $existing_period_attendance = isset($existing_period_attendances[$student_id][$data['period']]) ? $existing_period_attendances[$student_id][$data['period']]->first() : null;
                                    $leave_application_id = $student_leave_application_periods[$data['period']]->leave_application_id ?? null;

                                    if (!isset($existing_period_attendance) && $data['is_empty'] == false && $data['class_attendance_required'] == true) {
                                        $period_attendance_to_be_created[] = [
                                            'student_id' => $student_id,
                                            'date' => $date,
                                            'timeslot_id' => $data['timeslot_id'],
                                            'timeslot_type' => $data['timeslot_type'],
                                            'period' => $data['period'],
                                            'updated_by_employee_id' => null,
                                            'attendance_taken_at' => null,
                                            'status' => $data['default_init_status'],
                                            'leave_application_id' => $leave_application_id,
                                            'has_mark_deduction' => $data['has_mark_deduction'],
                                            'created_at' => now(),
                                            'updated_at' => now(),
                                        ];
                                    }
                                }
                            }
                        }

                        foreach (array_chunk($attendance_to_be_created, 2000) as $data) {
                            $this->attendanceRepository->insert($data);
                        }
                        foreach (array_chunk($period_attendance_to_be_created, 2000) as $data) {
                            $this->periodAttendanceRepository->insert($data);
                        }
                    });
                });

            $this->info('Attendance records initialized successfully for period ' . $date);
            LogHelper::write('Attendance records initialized successfully for period ' . $date, null, LogLevel::INFO, LogHelper::LOG_NAME_ATTENDANCE);

        } catch (\Exception $e) {
            LogHelper::write('Error initializing attendance records: ' . $e->getMessage() . $e->getTraceAsString(), null, LogLevel::ERROR, LogHelper::LOG_NAME_ATTENDANCE);
        }
    }
}
