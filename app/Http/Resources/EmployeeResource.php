<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class EmployeeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'user' => new UserResource($this->user),
            'name' => $this->name,
            'phone_number' => $this->phone_number,
            'email' => $this->email,
            'personal_email' => $this->personal_email,
            'nric' => $this->nric,
            'passport_number' => $this->passport_number,
            'employee_number' => (string) $this->employee_number ?: null,
            'badge_no' => (string) $this->badge_no ?: null,
            'job_title' => new EmployeeJobTitleResource($this->jobTitle),
            'date_of_birth' => (string) $this->date_of_birth ?: null,
            'gender' => $this->gender?->value,
            'status' => $this->status?->value,
            'religion' => new ReligionResource($this->religion),
            'race' => new RaceResource($this->race),
            'address' => (string) $this->address ?: null,
            'address_2' => (string) $this->address_2 ?: null,
            'postal_code' => (string) $this->postal_code ?: null,
            'city' => (string) $this->city ?: null,
            'state' => new StateResource($this->state),
            'country' => new CountryResource($this->country),
            'epf_number' => (string) $this->epf_number ?: null,
            'employment_start_date' => $this->employment_start_date,
            'employment_end_date' => $this->employment_end_date,
            'highest_education' => $this->highest_education,
            'highest_education_country' => new CountryResource($this->highestEducationCountry),
            'employment_type' => $this->employment_type,
            'employee_category' => new EmployeeCategoryResource($this->employeeCategory),
            'marriage_status' => $this->marriage_status,
            'is_hostel' => (bool) $this->is_hostel,
            'photo' => (string) $this->photo ?: null,
            'translations' => $this->translations,

            'active_hostel_bed_assignment' => $this->whenLoaded('activeHostelBedAssignments', function () {
                return new HostelBedAssignmentResource($this->activeHostelBedAssignments->first());
            }),

            'employment_history' => $this->whenLoaded('employmentHistories', function () {
                return EmploymentHistoryResource::collection($this->employmentHistoriesLatestFirst());
            }),

            'employee_session' => new EmployeeSessionResource($this->whenLoaded('employeeSession')),

            'teaching_periods' => $this->whenHas('teaching_periods'),
        ];
    }
}
