<?php

namespace App\Http\Requests\Api\Reports\Academy;

use App\Enums\ExportType;
use App\Models\Grade;
use App\Models\ResultsPostingHeader;
use App\Models\SemesterSetting;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class AllPassedStudentReportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'report_language' => ['required', 'exists:master_internationalization,code'],
            'export_type' => ['nullable', Rule::in(ExportType::values())],
            'semester_setting_id' => ['required', Rule::exists(SemesterSetting::class, 'id')],
            'result_posting_header_id' => ['required', Rule::exists(ResultsPostingHeader::class, 'id')],
            'grade_id' => ['required', Rule::exists(Grade::class, 'id')]
        ];
    }
}
