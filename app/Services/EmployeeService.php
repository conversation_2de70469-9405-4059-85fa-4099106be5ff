<?php

namespace App\Services;

use App\Enums\EmployeeStatus;
use App\Enums\LibraryMemberType;
use App\Helpers\ErrorCodeHelper;
use App\Interfaces\StatusChangeable;
use App\Interfaces\StatusChangeableInterface;
use App\Models\Config;
use App\Models\Employee;
use App\Models\EmployeeJobTitle;
use App\Models\PendingStudentEmployeeStatusChange;
use App\Repositories\EcommerceProductTagTargetRepository;
use App\Repositories\EmployeeRepository;
use App\Repositories\EmploymentHistoryRepository;
use App\Repositories\PendingStudentEmployeeStatusChangeRepository;
use App\Repositories\SubstituteRecordRepository;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class EmployeeService extends StatusChangeableBaseService implements StatusChangeableInterface
{
    protected Employee $employee;
    private array $api_request = [];
    private array $userData = [];
    private array $employeeData = [];
    private EmployeeJobTitle $newJobTitle;

    public function __construct(
        protected EmployeeRepository $employeeRepository,
        protected UserService $userService,
        protected DocumentRunningNumberService $documentRunningNumberService,
        protected ConfigService $configService,
        protected LibraryMemberService $libraryMemberService,
        protected EcommerceProductTagTargetRepository $ecommerceProductTagTargetRepository,
        protected PendingStudentEmployeeStatusChangeRepository $pendingStudentEmployeeStatusChangeRepository,
        protected SubstituteRecordRepository $substituteRecordRepository,
    ) {
    }

    public function getAllEmployees($filters = []): Collection
    {
        return $this->employeeRepository->getAll($filters);
    }

    public function getAllPaginatedEmployees($filters = []): LengthAwarePaginator
    {
        return $this->employeeRepository->getAllPaginated($filters);
    }

    public function createEmployee(): ?Model
    {
        return DB::transaction(function () {
            $user = $this->userService->createUser($this->getUserData(), Employee::class);

            $this->employeeData['user_id'] = $user->id;
            $this->employeeData['status'] = EmployeeStatus::WORKING;

            $this->employeeData['employee_number'] = $this->documentRunningNumberService
                ->setDocumentType(Employee::class)
                ->addPresetComponent(DocumentRunningNumberService::PLACEHOLDER_RUNNING_NUMBER)
                ->generate();

            $employee = $this->employeeRepository->create($this->getEmployeeData());

            $library_member = $this->libraryMemberService->createLibraryMember([
                'type' => LibraryMemberType::EMPLOYEE->value,
                'employee_id' => $employee->id,
                'member_number' => $employee->employee_number,
                'borrow_limit' => $this->configService->getConfigValue(Config::LIBRARY_BORROW_LIMIT_EMPLOYEE),
                'is_librarian' => false,
                'register_date' => now(),
                'valid_from' => now(),
                'valid_to' => now()->addYear(),
                'is_active' => true,
                'name' => $employee->translations['name'],
                'gender' => $employee->gender,
                'nric' => $employee->nric,
                'passport_number' => $employee->passport_number,
                'date_of_birth' => $employee->date_of_birth,
                'race_id' => $employee->race_id,
                'religion_id' => $employee->religion_id,
                'address' => $employee->address,
                'postcode' => $employee->postal_code,
                'city' => $employee->city,
                'state_id' => $employee->state_id,
                'country_id' => $employee->country_id,
                'phone_number' => $employee->phone_number,
                'email' => $employee->email,
            ]);

            if (!empty($this->employeeData['photo']) && is_file($this->employeeData['photo'])) {
                $employee->replaceMedia('photo', $this->employeeData['photo']);
                // copy over employee photo to library member
                $media = $employee->getFirstMedia('photo');
                $media->copy($library_member, 'photo');
            }

            return $employee;
        });
    }

    public function updateEmployee($id): ?Model
    {
        return DB::transaction(function () use ($id, &$updated_employee_job_title) {
            $employee = $this->employeeRepository->update($id, $this->getEmployeeData());

//            $this->userService->updateUser($employee->user_id, $data);

            if (!empty($this->employeeData['photo']) && is_file($this->employeeData['photo'])) {
                $employee->replaceMedia('photo', $this->employeeData['photo']);
            }

            if ($employee->status == EmployeeStatus::RESIGNED) {
                $this->ecommerceProductTagTargetRepository->deleteByUserable($employee);
            }

            return $employee;
        });
    }

    public function deleteEmployee($id): bool
    {
        return $this->employeeRepository->delete($id);
    }

    public function exists($filter): bool
    {
        return $this->employeeRepository->exists($filter);
    }

    public function markEmployeeResignedAt($date_string)
    {
        $this->validateLeaveOrResign($date_string);

        if ($this->isFutureDate($date_string)) {
            // create or update pending student employee status change, and cronjob will execute it on the effective date
            $this->pendingStudentEmployeeStatusChangeRepository->updateOrCreate(
                [
                    'type' => PendingStudentEmployeeStatusChange::TYPE_LEAVE,
                    'status' => PendingStudentEmployeeStatusChange::STATUS_PENDING,
                    'status_changeable_type' => Employee::class,
                    'status_changeable_id' => $this->employee->id,
                ],
                [
                    'execution_date' => $date_string,
                    'data' => $this->getApiRequest(),
                ]
            );
        } else {
            $this->leaveOrResign($date_string);
        }

    }

    public function markEmployeeReinstatedAt($date_string)
    {
        $this->validateReturnOrReinstate();

        if ($this->employee->user_id === null) {
            // when reinstating employee with no user account, create a user
            $user = $this->userService->createUser([
                'email' => $this->employee->email,
                'phone_number' => $this->employee->phone_number,
            ], Employee::class);

            $this->employeeRepository->update($this->employee, [
                'user_id' => $user->id,
            ]);
        }

        if ($this->isFutureDate($date_string)) {
            // create or update pending student employee status change, and cronjob will execute it on the effective date
            $this->pendingStudentEmployeeStatusChangeRepository->updateOrCreate(
                [
                    'type' => PendingStudentEmployeeStatusChange::TYPE_RETURN,
                    'status' => PendingStudentEmployeeStatusChange::STATUS_PENDING,
                    'status_changeable_type' => Employee::class,
                    'status_changeable_id' => $this->employee->id,
                ],
                [
                    'execution_date' => $date_string,
                    'data' => $this->getApiRequest(),
                ]
            );
        } else {
            $this->returnOrReinstate($date_string);
        }

    }

    public function transferEmployee($effective_date_string)
    {
        $this->validateTransfer($effective_date_string);

        if ($this->isFutureDate($effective_date_string)) {
            // create or update pending student employee status change, and cronjob will execute it on the effective date
            $this->pendingStudentEmployeeStatusChangeRepository->updateOrCreate(
                [
                    'type' => PendingStudentEmployeeStatusChange::TYPE_TRANSFER,
                    'status' => PendingStudentEmployeeStatusChange::STATUS_PENDING,
                    'status_changeable_type' => Employee::class,
                    'status_changeable_id' => $this->employee->id,
                ],
                [
                    'execution_date' => $effective_date_string,
                    'data' => $this->getApiRequest(),
                ]
            );
        } else {
            $this->transfer($effective_date_string);
        }

    }

    public function validateLeaveOrResign($effective_date_string): self
    {
        if ($this->employee->status !== EmployeeStatus::WORKING) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::EMPLOYEE_ERROR, 27001);
        }
        if (Carbon::parse($effective_date_string)->isBefore(Carbon::parse($this->employee->employment_start_date))) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::EMPLOYEE_ERROR, 27002);
        }

        return $this;
    }

    public function validateReturnOrReinstate(): self
    {
        if ($this->employee->status !== EmployeeStatus::RESIGNED) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::EMPLOYEE_ERROR, 27003);
        }

        return $this;
    }

    public function validateTransfer($effective_date_string): self
    {
        if ($this->employee->status !== EmployeeStatus::WORKING) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::EMPLOYEE_ERROR, 27004);
        }
        if (Carbon::parse($effective_date_string)->isBefore(Carbon::parse($this->employee->employment_start_date))) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::EMPLOYEE_ERROR, 27005);
        }
        if (!isset($this->newJobTitle)) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::EMPLOYEE_ERROR, 27006);
        }

        return $this;
    }

    public function returnOrReinstate($reinstate_date): self
    {
        $this->employeeRepository->update($this->employee, [
            'employment_start_date' => $reinstate_date,
            'employment_end_date' => null,
            'status' => EmployeeStatus::WORKING,
        ]);

        return $this;
    }

    public function leaveOrResign($effective_date): self
    {
        DB::transaction(function () use ($effective_date) {

            // update employee employment history table
            app()->make(EmploymentHistoryRepository::class)
                ->create([
                    'employee_id' => $this->employee->id,
                    'employment_end_date' => $effective_date,
                    'employment_start_date' => $this->employee->employment_start_date,
                    'job_title_id' => $this->employee->job_title_id,
                ]);

            // mark employee as resigned
            $this->employeeRepository->update($this->employee, [
                'status' => EmployeeStatus::RESIGNED,
                'employment_end_date' => $effective_date,
            ]);
        });

        return $this;
    }

    public function transfer($effective_date_string): self
    {
        if ($this->getNewJobTitle() === null) {
            throw new \Exception('Please specify new job title');
        }

        DB::transaction(function () use ($effective_date_string) {
            // update employee employment history table
            app()->make(EmploymentHistoryRepository::class)
                ->create([
                    'employee_id' => $this->employee->id,
                    'employment_start_date' => $this->employee->employment_start_date,
                    'employment_end_date' => Carbon::parse($effective_date_string)->subDay()->toDateString(),       // -1 day from new job start date
                    'job_title_id' => $this->employee->job_title_id,
                ]);

            $this->employeeRepository->update($this->employee, [
                'employment_start_date' => $effective_date_string,
                'employment_end_date' => null,
                'status' => EmployeeStatus::WORKING,
                'job_title_id' => $this->getNewJobTitle()->id,
            ]);
        });

        return $this;
    }

    public function setStatusChangeable(StatusChangeable $employee): self
    {
        $this->employee = $employee;
        return $this;
    }

    public function getEmployee(): Employee
    {
        return $this->employee;
    }

    public function setEmployee(Employee $employee): EmployeeService
    {
        $this->employee = $employee;
        return $this;
    }

    public function getApiRequest(): array
    {
        return $this->api_request;
    }

    public function setApiRequest($api_request): self
    {
        $this->api_request = $api_request;
        return $this;
    }

    public function getNewJobTitle(): EmployeeJobTitle
    {
        return $this->newJobTitle;
    }

    public function setNewJobTitle(EmployeeJobTitle $new_job_title): self
    {
        $this->newJobTitle = $new_job_title;
        return $this;
    }

    public function setUserData($data): self
    {
        $this->userData = [
            'email' => $data['email'] ?? null,
            'phone_number' => $data['phone_number'] ?? null,
            'password' => $input['password'] ?? null
        ];
        return $this;
    }

    public function getUserData(): array
    {
        return $this->userData;
    }

    public function setEmployeeData($data): self
    {
        $this->employeeData = [
            'name' => $data['name'],
            'phone_number' => $data['phone_number'],
            'email' => $data['email'],
            'personal_email' => $data['personal_email'] ?? null,
            'nric' => $data['nric'] ?? null,
            'passport_number' => $data['passport_number'] ?? null,
            'badge_no' => $data['badge_no'] ?? null,
            'job_title_id' => $data['job_title_id'],
            'date_of_birth' => $data['date_of_birth'],
            'gender' => $data['gender'],
            'religion_id' => $data['religion_id'] ?? null,
            'race_id' => $data['race_id'] ?? null,
            'address' => $data['address'],
            'address_2' => $data['address_2'],
            'postal_code' => $data['postal_code'] ?? null,
            'city' => $data['city'] ?? null,
            'state_id' => $data['state_id'] ?? null,
            'country_id' => $data['country_id'] ?? null,
            'photo' => $data['photo'] ?? null,
            'is_hostel' => $data['is_hostel'],
            'epf_number' => $data['epf_number'] ?? null,
            'employment_start_date' => $data['employment_start_date'],
            'employment_end_date' => $data['employment_end_date'] ?? null,
            'highest_education' => $data['highest_education'] ?? null,
            'highest_education_country_id' => $data['highest_education_country_id'] ?? null,
            'employment_type' => $data['employment_type'],
            'employee_category_id' => $data['employee_category_id'],
            'marriage_status' => $data['marriage_status'] ?? null,
            'employee_session_id' => $data['employee_session_id'] ?? null,
        ];

        return $this;
    }

    public function getEmployeeData(): array
    {
        return $this->employeeData;
    }

    public function retrieveTeachingPeriods(Collection|LengthAwarePaginator $employees, $teaching_period_from, $teaching_period_to): LengthAwarePaginator|Collection
    {
        $teaching_period = CarbonPeriod::create($teaching_period_from, $teaching_period_to);
        $days = [];

        foreach ($teaching_period as $day) {
            $days[] = Str::upper($day->englishDayOfWeek);
        }

        $employees->load([
            'timetables' => function (HasMany $query) use ($days) {
                $query->whereIn('day', $days)
                    ->whereNotNull('timeslot_id');
            }
        ]);

        // Get all substitute records that happen between the period
        $substitute_records = $this->substituteRecordRepository->getAll([
            'includes' => ['timeslot.period'],
            'substitute_date_from' => $teaching_period_from,
            'substitute_date_to' => $teaching_period_to,
        ])->groupBy('substitute_teacher_id');

        $substitute_records->transform(function ($substitute_record) {
            return $substitute_record->pluck('timeslot.period.period')->toArray();
        });

        foreach ($employees as $employee) {
            $employee_teaching_period_by_date = [];

            foreach ($teaching_period as $day) {
                $employee_teaching_period = $employee->timetables->where('day', Str::upper($day->englishDayOfWeek))
                    ->pluck('period')
                    ->toArray();

                $employee_teaching_period = array_merge($employee_teaching_period, $substitute_records[$employee->id] ?? []);

                sort($employee_teaching_period);

                $employee_teaching_period_by_date[] = [
                    'date' => $day->toDateString(),
                    'periods' => $employee_teaching_period,
                ];
            }

            $employee->setAttribute('teaching_periods', $employee_teaching_period_by_date);
        }

        return $employees;
    }
}
