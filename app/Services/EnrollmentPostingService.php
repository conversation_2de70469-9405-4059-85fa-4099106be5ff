<?php

namespace App\Services;

use App\Adapters\ExcelExportAdapter;
use App\Exports\EnrollmentPostingAutocountExport;
use App\Helpers\ErrorCodeHelper;
use App\Models\BillingDocument;
use App\Models\Enrollment;
use App\Repositories\BillingDocumentRepository;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

class EnrollmentPostingService
{
    protected ?string $paymentDateFrom;
    protected ?string $paymentDateTo;

    protected ?string $type;
    protected ?string $subType;
    protected ?string $status;
    protected ?string $referenceNo;

    protected ?Collection $dataset;

    public function __construct(
        protected BillingDocumentRepository $billingDocumentRepository
    )
    {
    }

    public function init()
    {
        $this->paymentDateFrom = null;
        $this->paymentDateTo = null;
        $this->type = null;
        $this->subType = null;
        $this->status = null;
        $this->referenceNo = null;
        $this->dataset = null;

        return $this;
    }

    public function query()
    {
        if (!isset($this->paymentDateFrom) || !isset($this->paymentDateTo)) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::ACCOUNTING_ERROR, 36012);
        }

        $relationships = [
            'billTo' => function (MorphTo $morphTo) {
                $morphTo->morphWith([
                    Enrollment::class => [
                        'enrollmentSession' => function ($query) {
                            $query->select(['id', 'admission_year']);
                        },
                    ],
                ]);
            },
            'lineItems' => function ($query) {
                $query->select(['id', 'billing_document_id', 'description', 'discount_id', 'offset_billing_document_id', 'gl_account_code', 'billable_item_type', 'billable_item_id']);
                $query->with([
                    'billableItem' => function ($query) {
                        $query->select(['id', 'period', 'description', 'product_id']);
                        $query->with([
                            'product' => function ($query) {
                                $query->select(['id', 'name']);
                            },
                        ]);
                    },
                ]);
                $query->orderBy('id', 'asc');
            },
            'payments' => function ($query) {
                $query->select(['id', 'billing_document_id', 'payment_method_id', 'payment_reference_no']);
                $query->with([
                    'paymentMethod' => function ($query) {
                        $query->select(['id', 'code']);
                    },
                 ]);
            },
        ];

        $payment_date_from_utc = Carbon::parse($this->paymentDateFrom, config('school.timezone'))->startOfDay()->tz('UTC')->toDateTimeString();
        $payment_date_to_utc = Carbon::parse($this->paymentDateTo, config('school.timezone'))->endOfDay()->tz('UTC')->toDateTimeString();

        $this->dataset = $this->billingDocumentRepository
            ->with($relationships)
            ->getAll([
                'paid_at_from' => $payment_date_from_utc,
                'paid_at_to' => $payment_date_to_utc,
                'type' => $this->type ?? null,
                'sub_type' => BillingDocument::SUB_TYPE_ENROLLMENT_FEES, // only for enrollments
                'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
                'status' => $this->status ?? null,
                'reference_no' => $this->referenceNo ?? null,
                'order_by' => [
                    'document_date' => 'ASC',
                ]
            ]);

        return $this;
    }

    public function generateExcelAndGetFileUrl(): ?string
    {
        if (!isset($this->dataset) || !isset($this->paymentDateFrom) || !isset($this->paymentDateTo)) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::ACCOUNTING_ERROR, 36012);
        }

        if ($this->dataset->count() === 0) {
            return null;
        }

        $filename = 'autocount-posting-' . Carbon::parse($this->paymentDateFrom)->format('Ymd') . '-' . Carbon::parse($this->paymentDateTo)->format('Ymd') . '-' . md5(Auth::user()->id . Carbon::now()->timestamp);

        $adapter = new ExcelExportAdapter();
        $adapter->setReportBuilder(new EnrollmentPostingAutocountExport($this->dataset));

        $service = app()->make(BillingDocumentPostingPrintService::class);

        return $service->setExportFileAdapter($adapter)
            ->setFileName($filename)
            ->generate()
            ->upload()
            ->getFileUrl();
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;
        return $this;
    }

    public function getSubType(): string
    {
        return $this->subType;
    }

    public function setSubType(string $sub_type): self
    {
        $this->subType = $sub_type;
        return $this;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;
        return $this;
    }

    public function getReferenceNo(): string
    {
        return $this->referenceNo;
    }

    public function setReferenceNo(string $reference_no): self
    {
        $this->referenceNo = $reference_no;
        return $this;
    }

    public function getDataset(): Collection
    {
        return $this->dataset;
    }

    public function setDataset(Collection $dataset): self
    {
        $this->dataset = $dataset;
        return $this;
    }

    public function getPaymentDateFrom(): ?string
    {
        return $this->paymentDateFrom;
    }

    public function setPaymentDateFrom(?string $payment_date_from): self
    {
        $this->paymentDateFrom = $payment_date_from;
        return $this;
    }

    public function getPaymentDateTo(): ?string
    {
        return $this->paymentDateTo;
    }

    public function setPaymentDateTo(?string $payment_date_to): self
    {
        $this->paymentDateTo = $payment_date_to;
        return $this;
    }
}
