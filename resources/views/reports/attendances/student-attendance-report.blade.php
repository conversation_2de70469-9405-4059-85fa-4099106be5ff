@php
    use App\Enums\AttendanceStatus;
    use App\Helpers\ConfigHelper;
@endphp
@extends('reports.layout')

@section('content')
    <div style="text-align: center; margin-bottom: 10px;">
        <h4>{{ $data[0]['class_name'] }}</h4>
    </div>
    @php
        $available_locales = ConfigHelper::getAvailableLocales();
        $header_colspan = 7 + count($available_locales);
    @endphp
    @foreach($data as $class)
        <table>
            <thead>
            <tr>
                <th>{{__('general.student_no')}}</th>
                <th>{{__('general.student_class')}}</th>
                @foreach($available_locales as $locale)
                    <th>{{__('general.student_name')}}</th>
                @endforeach
                <th>{{__('attendance.time_in')}}</th>
                <th>{{__('attendance.time_out')}}</th>
                <th>{{__('attendance.attendance_status')}}</th>
                <th>{{__('general.reason')}}</th>
                <th>{{__('general.remarks')}}</th>
            </tr>
            </thead>

            <tbody>
            @foreach($class['dates'] as $date)
                <tr>
                    <td colspan="{{$header_colspan}}">{{$date['date']}}</td>
                </tr>

                @foreach($date['students'] as $student)
                    @php
                        $row_style = '';

                        if($student['attendance_reason'] != null) {
                            $row_style = 'background-color: yellow;';
                        } elseif ($student['attendance_status'] == AttendanceStatus::ABSENT) {
                            $row_style = 'background-color: red; color: white;';
                        }
                    @endphp
                    <tr style="{{ $row_style }}">
                        <td>{{$student['student_number']}}</td>
                        <td>{{$student['student_primary_class']}}</td>
                        @foreach($available_locales as $locale)
                            <td>{{$student['student_name'][$locale] ?? ''}}</td>
                        @endforeach
                        <td>{{$student['attendance_time_in']}}</td>
                        <td>{{$student['attendance_time_out']}}</td>
                        <td>{{$student['attendance_status']}}</td>
                        <td>{{$student['attendance_reason']}}</td>
                        <td>{{$student['attendance_remarks']}}</td>
                    </tr>
                @endforeach
                <tr>
                    <td colspan="2">{{__('general.total')}} : {{$date['total']}}</td>
                    <td colspan="{{ count($available_locales) }}">{{__('attendance.attend')}} : {{$date['total_present']}}</td>
                    <td colspan="2">{{__('attendance.absent')}} : {{$date['total_absent']}}</td>
                    <td colspan="3">{{__('attendance.late')}} : {{$date['total_late']}}</td>
                </tr>
            @endforeach

            </tbody>
        </table>

        @if(!$loop->last)
            <div style="page-break-after: always"></div>
        @endif
    @endforeach
@endsection
