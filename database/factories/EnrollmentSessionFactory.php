<?php

namespace Database\Factories;

use App\Models\Course;
use App\Models\Grade;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\EnrollmentSession>
 */
class EnrollmentSessionFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => 'Session ' . now()->format('Y-m-d') . ' ' . now()->timestamp,
            'from_date' => now()->toDateString(),
            'to_date' => now()->addDays(90)->toDateString(),
            'code' => 'SESSION_' . now()->timestamp . fake()->uuid(),
            'is_active' => false,
            'course_id' => Course::factory(),
            'fee_assignment_settings' => null,
            'admission_year' => now()->year,
            'admission_grade_id' => Grade::factory(),
        ];
    }
}
